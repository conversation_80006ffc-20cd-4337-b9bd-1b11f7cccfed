#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接状态监听功能测试脚本

这个脚本用于测试新实现的连接状态监听系统，验证：
1. 物理断开检测
2. UI自动更新
3. 重连功能
4. 连接按钮状态同步

使用方法：
1. 确保设备已连接
2. 运行此脚本
3. 在运行过程中物理断开设备连接
4. 观察控制台输出和UI变化
5. 重新连接设备，观察自动重连功能
"""

import sys
import os
import time
import threading
from PyQt5 import QtCore, QtWidgets, QtGui

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from mdp_gui import MDPMainwindow, ConnectionStatusMonitor
    from controller import find_all_devices, PDPocket
    from loguru import logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在gui_source目录下运行此脚本")
    sys.exit(1)


class ConnectionTestWindow(QtWidgets.QWidget):
    """连接测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.api = None
        self.connection_monitor = None
        self.setup_ui()
        self.setup_connection_monitor()
        
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("连接状态监听测试")
        self.setGeometry(100, 100, 600, 400)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # 状态显示
        self.status_label = QtWidgets.QLabel("状态: 未连接")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 连接信息
        self.info_label = QtWidgets.QLabel("连接信息: 无")
        layout.addWidget(self.info_label)
        
        # 按钮区域
        button_layout = QtWidgets.QHBoxLayout()
        
        self.connect_btn = QtWidgets.QPushButton("连接设备")
        self.connect_btn.clicked.connect(self.toggle_connection)
        button_layout.addWidget(self.connect_btn)
        
        self.test_btn = QtWidgets.QPushButton("开始监听测试")
        self.test_btn.clicked.connect(self.start_monitoring_test)
        button_layout.addWidget(self.test_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        self.log_text = QtWidgets.QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        # 测试说明
        instructions = QtWidgets.QLabel("""
测试说明：
1. 点击"连接设备"连接到WPX设备
2. 点击"开始监听测试"开始监听连接状态
3. 物理断开设备连接（拔掉USB线）
4. 观察状态变化和日志输出
5. 重新连接设备，观察自动重连功能
        """)
        instructions.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(instructions)
        
    def setup_connection_monitor(self):
        """设置连接状态监听器"""
        self.connection_monitor = ConnectionStatusMonitor(self)
        self.connection_monitor.connection_status_changed.connect(self.on_connection_status_changed)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(log_entry)  # 同时输出到控制台
        
    def toggle_connection(self):
        """切换连接状态"""
        if self.api is None:
            self.connect_device()
        else:
            self.disconnect_device()
            
    def connect_device(self):
        """连接设备"""
        try:
            self.log_message("正在搜索设备...")
            devices = find_all_devices()
            
            if not devices:
                self.log_message("未找到设备")
                QtWidgets.QMessageBox.warning(self, "警告", "未找到WPX设备")
                return
                
            # 连接第一个找到的设备
            port, description = devices[0]
            self.log_message(f"尝试连接到: {port} - {description}")
            
            self.api = PDPocket(port=port, baudrate=115200, use_wpx_protocol=True)
            self.connection_monitor.set_api(self.api)
            
            self.log_message(f"成功连接到设备: {port}")
            self.update_ui_connected(True, f"{port} - {description}")
            
        except Exception as e:
            self.log_message(f"连接失败: {e}")
            QtWidgets.QMessageBox.critical(self, "错误", f"连接失败: {e}")
            
    def disconnect_device(self):
        """断开设备连接"""
        try:
            if self.api:
                self.api.close()
                self.api = None
                
            self.connection_monitor.set_api(None)
            self.log_message("已断开设备连接")
            self.update_ui_connected(False, "无")
            
        except Exception as e:
            self.log_message(f"断开连接时出错: {e}")
            
    def update_ui_connected(self, connected, info):
        """更新UI连接状态"""
        if connected:
            self.status_label.setText("状态: 已连接")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: green;")
            self.connect_btn.setText("断开连接")
            self.connect_btn.setStyleSheet("background-color: #ff6b6b; color: white;")
        else:
            self.status_label.setText("状态: 未连接")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: red;")
            self.connect_btn.setText("连接设备")
            self.connect_btn.setStyleSheet("background-color: #4CAF50; color: white;")
            
        self.info_label.setText(f"连接信息: {info}")
        
    def on_connection_status_changed(self, connected):
        """连接状态变化回调"""
        status = "已连接" if connected else "断开连接"
        self.log_message(f"连接状态变化: {status}")
        
        # 更新UI（注意：这个回调可能在非主线程中调用）
        QtCore.QMetaObject.invokeMethod(
            self, 
            "update_connection_status_ui", 
            QtCore.Qt.QueuedConnection,
            QtCore.Q_ARG(bool, connected)
        )
        
    @QtCore.pyqtSlot(bool)
    def update_connection_status_ui(self, connected):
        """在主线程中更新连接状态UI"""
        if connected:
            self.log_message("✅ 设备重新连接成功")
        else:
            self.log_message("❌ 检测到设备断开连接")
            self.update_ui_connected(False, "连接丢失")
            
    def start_monitoring_test(self):
        """开始监听测试"""
        if self.api is None:
            QtWidgets.QMessageBox.warning(self, "警告", "请先连接设备")
            return
            
        self.log_message("开始连接状态监听测试...")
        self.log_message("请物理断开设备连接（拔掉USB线）来测试断开检测功能")
        
        # 启动监听线程
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
    def monitoring_loop(self):
        """监听循环"""
        while self.api is not None:
            try:
                # 使用连接监听器检查状态
                self.connection_monitor.check_connection_status()
                time.sleep(0.1)  # 100ms检查一次
            except Exception as e:
                self.log_message(f"监听循环出错: {e}")
                break


def main():
    """主函数"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("连接状态监听测试")
    app.setApplicationVersion("1.0")
    
    # 创建测试窗口
    window = ConnectionTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
