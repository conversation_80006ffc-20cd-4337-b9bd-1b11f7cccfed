"""
最简单的连接监听实现
直接复制粘贴到mdp_gui.py中即可使用
"""

# ========== 第一步：在MDPMainwindow类的__init__方法末尾添加 ==========

# 连接状态监听初始化
self.last_data_time = time.time()
self.connection_lost_notified = False

# 创建连接检查定时器
self.connection_check_timer = QtCore.QTimer(self)
self.connection_check_timer.timeout.connect(self.check_connection_timeout)
self.connection_check_timer.start(1000)  # 每秒检查一次

print("连接状态监听已启用")


# ========== 第二步：在MDPMainwindow类中添加以下两个方法 ==========

def check_connection_timeout(self):
    """检查连接超时"""
    if not self.api:
        return
        
    # 检查距离上次数据更新的时间
    time_since_data = time.time() - self.last_data_time
    
    # 如果超过2秒没有数据，判定为连接断开
    if time_since_data > 2.0 and not self.connection_lost_notified:
        print(f"数据超时 {time_since_data:.1f}秒，自动断开连接")
        
        # 清理连接
        self.api = None
        self.connection_lost_notified = True
        
        # 更新UI
        self.close_state_ui()
        if hasattr(self.ui, 'btnConnect'):
            self.ui.btnConnect.setText("连接")
        if hasattr(self.ui, 'labelConnectState'):
            self.ui.labelConnectState.setText("未连接")
            
        # 显示提示
        if hasattr(self, 'statusbar'):
            self.statusbar.showMessage("设备连接已断开", 3000)

def mark_data_received(self):
    """标记收到数据"""
    self.last_data_time = time.time()
    if self.connection_lost_notified:
        self.connection_lost_notified = False
        print("连接已恢复")


# ========== 第三步：在所有数据更新方法开头添加一行代码 ==========

# 在以下方法的开头添加：self.mark_data_received()

# 1. update_state_callback 方法开头添加：
def update_state_callback(self, vout, iout, timestamp, output_on):
    self.mark_data_received()  # 添加这一行
    # ... 原有代码保持不变

# 2. state_callback 方法开头添加：
def state_callback(self, vout, iout, timestamp, output_on):
    self.mark_data_received()  # 添加这一行
    # ... 原有代码保持不变

# 3. update_full_wpx_data 方法开头添加：
def update_full_wpx_data(self, data):
    self.mark_data_received()  # 添加这一行
    # ... 原有代码保持不变

# 4. update_extended_data 方法开头添加：
def update_extended_data(self, data):
    self.mark_data_received()  # 添加这一行
    # ... 原有代码保持不变


# ========== 完整的集成示例 ==========

"""
完整的修改步骤：

1. 在MDPMainwindow类的__init__方法最后添加：
   
   # 连接状态监听
   self.last_data_time = time.time()
   self.connection_lost_notified = False
   self.connection_check_timer = QtCore.QTimer(self)
   self.connection_check_timer.timeout.connect(self.check_connection_timeout)
   self.connection_check_timer.start(1000)

2. 在MDPMainwindow类中添加两个新方法：
   - check_connection_timeout(self)
   - mark_data_received(self)

3. 在所有数据更新方法开头添加：
   self.mark_data_received()

就这么简单！不需要额外的文件，不需要复杂的逻辑。

工作原理：
- 每次收到数据时更新时间戳
- 定时器每秒检查一次，如果超过2秒没收到数据就自动断开
- 自动更新UI状态和按钮文本

这样就实现了您要求的功能：
"当物理连接断开时，UI界面能够自动同步更新为'未连接'状态，而无需用户手动点击按钮"
"""