#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的导入测试脚本，用于验证修复后的代码是否可以正常导入
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试关键模块的导入"""
    try:
        print("测试导入 mdp_gui...")
        from mdp_gui import MDPMainwindow, ConnectionStatusMonitor, MDPThread
        print("✅ mdp_gui 导入成功")
        
        print("测试导入 controller...")
        from controller import find_all_devices, PDPocket
        print("✅ controller 导入成功")
        
        print("测试创建 ConnectionStatusMonitor...")
        monitor = ConnectionStatusMonitor()
        print("✅ ConnectionStatusMonitor 创建成功")
        
        print("测试创建 MDPThread...")
        thread = MDPThread()
        print("✅ MDPThread 创建成功")
        
        print("\n🎉 所有测试通过！连接状态监听功能已成功集成。")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
